{"name": "test-employee", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5173", "test": "vitest", "test:watch": "vitest --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "postinstall": "cypress install", "e2e": "bash ./scripts/e2e.sh", "format": "prettier --write .", "prepare": "husky install", "typecheck": "tsc --noEmit && oxlint --type-aware", "lint": "pnpm run oxlint && pnpm run eslint", "eslint": "eslint --ext .ts,.tsx,.js,.jsx src --max-warnings=0", "lint:fix": "eslint --ext .ts,.tsx,.js,.jsx src --fix", "oxlint": "ox<PERSON>"}, "dependencies": {"@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/x-data-grid": "^8.11.0", "@mui/x-date-pickers": "^8.11.0", "@tanstack/react-form": "^1.19.3", "@tanstack/react-query": "^5.85.9", "idb": "^8.0.3", "luxon": "^3.7.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-error-boundary": "^6.0.0", "react-router-dom": "^7.8.2", "uuid": "^11.1.0", "zod": "^4.1.5"}, "devDependencies": {"@testing-library/cypress": "^10.1.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitejs/plugin-react": "^5.0.2", "cypress": "^15.1.0", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.6", "oxlint": "^1.14.0", "oxlint-tsgolint": "^0.1.5", "prettier": "^3.6.2", "start-server-and-test": "^2.0.13", "typescript": "^5.9.2", "vite": "^7.1.4", "vitest": "^3.2.4"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,json,css,scss,md,html}": ["prettier --write", "git add"]}, "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67"}