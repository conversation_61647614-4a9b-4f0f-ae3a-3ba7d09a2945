{"compilerOptions": {"target": "ES2021", "useDefineForClassFields": true, "lib": ["DOM", "ES2021"], "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["vitest/globals", "cypress", "@testing-library/jest-dom", "@testing-library/cypress"]}, "include": ["src", "vite.config.ts", "cypress/**/*.ts"]}