name: <PERSON><PERSON> — typecheck & lint

on:
  push: {}
  pull_request: {}

jobs:
  check:
    runs-on: ubuntu-latest
    env:
      CI: true
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Typecheck (tsc)
        run: pnpm run typecheck

      - name: <PERSON><PERSON> (oxlint then eslint)
        run: pnpm run lint
