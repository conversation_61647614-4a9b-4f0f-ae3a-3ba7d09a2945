name: <PERSON><PERSON> — Cypress E2E

on:
  push: {}
  pull_request: {}

jobs:
  e2e:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run Cypress E2E (starts dev server via scripts/e2e.sh)
        run: |
          chmod +x ./scripts/e2e.sh
          ./scripts/e2e.sh
