import { DateTime } from 'luxon';

export const mockEmployee = {
  firstName: '<PERSON>',
  lastName: 'Do<PERSON>',
  email: '<EMAIL>',
  phone: '91234567',
  gender: 'male' as const,
  dateOfBirth: DateTime.fromFormat('1990-01-01', 'yyyy-MM-dd').toJSDate(),
  joinedDate: DateTime.fromFormat('2020-01-01', 'yyyy-MM-dd').toJSDate(),
};

export const mockEmployee2 = {
  firstName: '<PERSON>',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  phone: '98765432',
  gender: 'female' as const,
  dateOfBirth: DateTime.fromFormat('1995-05-15', 'yyyy-MM-dd').toJSDate(),
  joinedDate: DateTime.fromFormat('2021-06-30', 'yyyy-MM-dd').toJSDate(),
};
